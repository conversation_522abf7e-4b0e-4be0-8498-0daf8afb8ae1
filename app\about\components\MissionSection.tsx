export function MissionSection() {
  return (
    <section className="py-16 px-6 bg-white">
      <div className="max-w-6xl mx-auto">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Our Mission
            </h2>
            <p className="text-lg text-gray-600 mb-6 leading-relaxed">
              We believe in the power of technology to transform businesses and improve lives. 
              Our mission is to deliver cutting-edge solutions that empower our clients to 
              achieve their goals and exceed their expectations.
            </p>
            <p className="text-lg text-gray-600 leading-relaxed">
              Through innovation, collaboration, and unwavering commitment to excellence, 
              we strive to be the trusted partner that helps organizations navigate the 
              digital landscape and unlock their full potential.
            </p>
          </div>
          <div className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">Our Values</h3>
            <ul className="space-y-3">
              <li className="flex items-center">
                <span className="w-2 h-2 bg-white rounded-full mr-3"></span>
                Innovation & Creativity
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-white rounded-full mr-3"></span>
                Customer-Centric Approach
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-white rounded-full mr-3"></span>
                Quality & Excellence
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-white rounded-full mr-3"></span>
                Integrity & Transparency
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-white rounded-full mr-3"></span>
                Continuous Learning
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
}
