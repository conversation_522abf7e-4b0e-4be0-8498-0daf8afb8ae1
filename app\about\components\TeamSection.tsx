export function TeamSection() {
  const teamMembers = [
    {
      name: "<PERSON>",
      role: "CEO & Founder",
      bio: "Visionary leader with 15+ years in tech industry, passionate about innovation and team building.",
      avatar: "<PERSON><PERSON>"
    },
    {
      name: "<PERSON>",
      role: "<PERSON><PERSON>",
      bio: "Technical expert specializing in scalable architectures and emerging technologies.",
      avatar: "<PERSON>"
    },
    {
      name: "<PERSON>",
      role: "Head of Design",
      bio: "Creative director focused on user-centered design and exceptional user experiences.",
      avatar: "ER"
    },
    {
      name: "<PERSON>",
      role: "Lead Developer",
      bio: "Full-stack developer with expertise in modern web technologies and best practices.",
      avatar: "DK"
    }
  ];

  return (
    <section className="py-16 px-6 bg-white">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Meet Our Team
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Our diverse team of experts brings together years of experience and a shared passion for excellence.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {teamMembers.map((member, index) => (
            <div key={index} className="text-center group">
              <div className="relative mb-4">
                <div className="w-32 h-32 mx-auto bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-2xl font-bold group-hover:scale-105 transition-transform">
                  {member.avatar}
                </div>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-1">
                {member.name}
              </h3>
              <p className="text-blue-600 font-semibold mb-3">
                {member.role}
              </p>
              <p className="text-gray-600 text-sm leading-relaxed">
                {member.bio}
              </p>
            </div>
          ))}
        </div>
        
        <div className="text-center mt-12">
          <p className="text-lg text-gray-600 mb-6">
            Want to join our amazing team?
          </p>
          <button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
            View Open Positions
          </button>
        </div>
      </div>
    </section>
  );
}
