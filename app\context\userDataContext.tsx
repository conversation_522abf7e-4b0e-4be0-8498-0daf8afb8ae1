
"use client";
import { createContext, useState } from "react";



type UserDataContextType = {
  userData: any;
  setUserData: React.Dispatch<React.SetStateAction<any>>;
};

export const UserDataContext = createContext<UserDataContextType>({
  userData: {},
  setUserData: () => {},
});

export const UserDataProvider = ({ children }: { children: React.ReactNode }) => {
  const [userData, setUserData] = useState({
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "user",
  });

  return (
    <UserDataContext.Provider value={{ userData, setUserData }}>
      {children}
    </UserDataContext.Provider>
  );
};
