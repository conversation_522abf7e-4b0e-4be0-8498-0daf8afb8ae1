"use client";
import { useContext } from "react";
import { UserDataContext } from "./context/userDataContext";

export default function Home() {
  const { userData, setUserData } = useContext(UserDataContext);

  return (
    <div className="flex flex-col gap-8 items-center sm:items-start">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 w-full max-w-2xl">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
          Home Page
        </h1>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-6">
          Welcome, {userData.name}!
        </p>
        <button
          onClick={() =>
            setUserData({
              name: "yassine",
              email: "<EMAIL>",
              role: "developer",
            })
          }
          className="px-6 py-3 bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white rounded-lg transition-colors duration-200 font-medium"
        >
          Change User
        </button>
      </div>

      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl">
        <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-3">
          User Information
        </h2>
        <div className="space-y-2 text-gray-700 dark:text-gray-300">
          <p><span className="font-medium">Name:</span> {userData.name}</p>
          <p><span className="font-medium">Email:</span> {userData.email}</p>
          <p><span className="font-medium">Role:</span> {userData.role}</p>
        </div>
      </div>
    </div>
  );
}
