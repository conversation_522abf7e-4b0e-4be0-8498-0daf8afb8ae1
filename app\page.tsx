"use client";
import { useContext } from "react";
import { UserDataContext } from "./context/userDataContext";

export default function Home() {
  const { userData, setUserData } = useContext(UserDataContext);

  return (
    <div
      className="flex flex-col gap-[32px] row-start-2 items-center sm:items-start"
    >
      <h1 className="text-3xl font-bold">Home Page</h1>
      <p>Welcome, {userData.name}!</p>
      <button
        onClick={() =>
          setUserData({
            name: "yassine",
            email: "<EMAIL>",
            role: "developer",
          })
        }
      >
        Change User
      </button>
    </div>
  );
}
